import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Send, Search, Plus, Smile, Paperclip, Video, Phone } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileMessagingInterface } from "./MobileMessagingInterface";
import { Badge } from "@/components/ui/badge";
type Message = {
  id: string;
  sender: string;
  content: string;
  timestamp: string;
  isCustomer: boolean;
};
type Conversation = {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    initials: string;
    status: 'online' | 'away' | 'offline';
  };
  jobTitle: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
  messages: Message[];
};
export function CustomerMessages() {
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const isMobile = useIsMobile();

  // If on mobile, render the mobile-optimized component
  if (isMobile) {
    return <MobileMessagingInterface />;
  }

  // Mock conversations data with status
  const conversations: Conversation[] = [{
    id: "conv1",
    provider: {
      name: "Mike's Plumbing",
      initials: "MP",
      status: 'online'
    },
    jobTitle: "Bathroom Renovation",
    lastMessage: "Can you confirm the appointment time?",
    timestamp: "11:42 AM",
    unread: 2,
    messages: [{
      id: "msg1",
      sender: "Mike's Plumbing",
      content: "Hi there! I've reviewed your bathroom renovation job request and I'm excited to work with you.",
      timestamp: "Yesterday, 2:30 PM",
      isCustomer: false
    }, {
      id: "msg2",
      sender: "You",
      content: "Great! What do you think about the timeline?",
      timestamp: "Yesterday, 3:15 PM",
      isCustomer: true
    }, {
      id: "msg3",
      sender: "Mike's Plumbing",
      content: "I think we can complete it within 3 weeks. Can you confirm the appointment time for the initial inspection?",
      timestamp: "Today, 11:42 AM",
      isCustomer: false
    }]
  }, {
    id: "conv2",
    provider: {
      name: "Green Thumb Landscaping",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
      initials: "GT",
      status: 'away'
    },
    jobTitle: "Lawn Maintenance",
    lastMessage: "Your quote is ready to review",
    timestamp: "Yesterday",
    unread: 0,
    messages: [{
      id: "msg1",
      sender: "Green Thumb Landscaping",
      content: "Hello! I've prepared a comprehensive quote for your lawn maintenance job.",
      timestamp: "Yesterday, 10:15 AM",
      isCustomer: false
    }, {
      id: "msg2",
      sender: "You",
      content: "Thanks! I'll take a look at it.",
      timestamp: "Yesterday, 11:30 AM",
      isCustomer: true
    }]
  }, {
    id: "conv3",
    provider: {
      name: "Elite Electricians",
      initials: "EE",
      status: 'offline'
    },
    jobTitle: "Lighting Installation",
    lastMessage: "Thank you for your payment",
    timestamp: "Apr 15",
    unread: 0,
    messages: [{
      id: "msg1",
      sender: "Elite Electricians",
      content: "Thank you for your payment. We've received it successfully and your project is now complete!",
      timestamp: "Apr 15, 9:20 AM",
      isCustomer: false
    }]
  }];

  // Filter conversations by search query
  const filteredConversations = conversations.filter(conv => conv.provider.name.toLowerCase().includes(searchQuery.toLowerCase()) || conv.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()));

  // Handle sending a new message
  const handleSendMessage = () => {
    if (!messageInput.trim() || !selectedConversation) return;

    // Clear input
    setMessageInput('');
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  };
  return <div className="min-h-screen p-6 bg-blue-50">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-2">
          <div className="p-3 bg-blue-600 rounded-xl shadow-lg">
            <Send className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Messages
            </h1>
            <p className="text-gray-600">Connect with your service providers</p>
          </div>
        </div>
      </div>

      <div className="flex flex-col md:flex-row h-[calc(100vh-12rem)] gap-6">
        {/* Conversations List */}
        <Card className="flex-none md:w-1/3 overflow-hidden flex flex-col border-0 shadow-xl bg-white">
          <CardHeader className="pb-4 border-b border-gray-100 bg-gray-50">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-gray-800">Conversations</CardTitle>
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white shadow-md">
                  <Plus className="h-4 w-4 mr-1" />
                  New
                </Button>
              </div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input placeholder="Search conversations..." className="pl-10 border-0 bg-white shadow-sm focus:shadow-md transition-shadow" value={searchQuery} onChange={e => setSearchQuery(e.target.value)} />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0 overflow-y-auto flex-grow">
            <div className="divide-y divide-gray-50">
              {filteredConversations.length > 0 ? filteredConversations.map(conversation => <div key={conversation.id} className={`p-4 cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:shadow-sm ${selectedConversation?.id === conversation.id ? 'bg-blue-50 border-r-4 border-blue-600 shadow-sm' : ''}`} onClick={() => setSelectedConversation(conversation)}>
                    <div className="flex items-start gap-3">
                      <div className="relative">
                        <Avatar className="h-12 w-12 ring-2 ring-white shadow-md">
                          {conversation.provider.avatar && <AvatarImage src={conversation.provider.avatar} alt={conversation.provider.name} />}
                          <AvatarFallback className="bg-indigo-500 text-white font-semibold">
                            {conversation.provider.initials}
                          </AvatarFallback>
                        </Avatar>
                        <div className={`absolute -bottom-0.5 -right-0.5 w-4 h-4 border-2 border-white rounded-full ${getStatusColor(conversation.provider.status)}`}></div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-baseline mb-1">
                          <h4 className="font-semibold text-gray-900 truncate">{conversation.provider.name}</h4>
                          <span className="text-xs text-gray-500 ml-2">{conversation.timestamp}</span>
                        </div>
                        <p className="text-sm text-blue-600 font-medium truncate mb-1">{conversation.jobTitle}</p>
                        <p className="text-sm text-gray-600 truncate">{conversation.lastMessage}</p>
                      </div>
                      {conversation.unread > 0 && <Badge className="bg-red-500 text-white shadow-md">
                          {conversation.unread}
                        </Badge>}
                    </div>
                  </div>) : <div className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Search className="h-8 w-8 text-gray-400" />
                  </div>
                  <p className="text-gray-500 font-medium">No conversations found</p>
                  <p className="text-sm text-gray-400 mt-1">Try adjusting your search</p>
                </div>}
            </div>
          </CardContent>
        </Card>

        {/* Message View */}
        <Card className="flex-grow flex flex-col border-0 shadow-xl bg-white">
          {selectedConversation ? <>
              {/* Conversation Header */}
              <CardHeader className="pb-4 border-b bg-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <Avatar className="h-12 w-12 ring-2 ring-white shadow-md">
                        {selectedConversation.provider.avatar && <AvatarImage src={selectedConversation.provider.avatar} alt={selectedConversation.provider.name} />}
                        <AvatarFallback className="bg-indigo-500 text-white font-semibold">
                          {selectedConversation.provider.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-0.5 -right-0.5 w-4 h-4 border-2 border-white rounded-full ${getStatusColor(selectedConversation.provider.status)}`}></div>
                    </div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">{selectedConversation.provider.name}</CardTitle>
                      <p className="text-sm font-medium text-blue-600">{selectedConversation.jobTitle}</p>
                      <p className="text-xs text-gray-500 capitalize">{selectedConversation.provider.status}</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="shadow-sm hover:shadow-md transition-shadow">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="shadow-sm hover:shadow-md transition-shadow">
                      <Video className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-grow overflow-y-auto p-6 bg-slate-25 bg-white">
                <div className="space-y-6">
                  {selectedConversation.messages.map(message => <div key={message.id} className={`flex ${message.isCustomer ? 'justify-start' : 'justify-end'}`}>
                      <div className={`max-w-[75%] ${message.isCustomer ? 'order-1' : 'order-2'}`}>
                        <div className={`px-4 py-3 rounded-2xl shadow-sm ${message.isCustomer ? 'bg-gray-200 text-gray-800 rounded-bl-sm' : 'bg-blue-600 text-white rounded-br-sm'}`}>
                          <p className="text-sm leading-relaxed">{message.content}</p>
                        </div>
                        <p className={`text-xs mt-1 px-1 ${message.isCustomer ? 'text-left text-gray-500' : 'text-right text-gray-500'}`}>
                          {message.timestamp}
                        </p>
                      </div>
                    </div>)}
                </div>
              </CardContent>

              {/* Message Input */}
              <div className="p-4 border-t bg-white">
                <div className="flex items-center gap-3">
                  <Button variant="outline" size="icon" className="shadow-sm hover:shadow-md transition-shadow">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  <div className="flex-1 relative">
                    <Input placeholder="Type your message..." className="pr-12 border-gray-200 shadow-sm focus:shadow-md transition-shadow bg-white" value={messageInput} onChange={e => setMessageInput(e.target.value)} onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }} />
                    <Button variant="ghost" size="icon" className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8">
                      <Smile className="h-4 w-4 text-gray-400" />
                    </Button>
                  </div>
                  <Button onClick={handleSendMessage} className="bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all" disabled={!messageInput.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </> : <CardContent className="flex flex-col items-center justify-center h-full text-center p-12 bg-gray-50">
              <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                <Send className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Select a conversation</h3>
              <p className="text-gray-500 max-w-md leading-relaxed">
                Choose a conversation from the list to start chatting with your service providers. 
                Your messages are secure and encrypted.
              </p>
              <Button className="mt-6 bg-blue-600 hover:bg-blue-700 text-white shadow-md">
                <Plus className="h-4 w-4 mr-2" />
                Start New Conversation
              </Button>
            </CardContent>}
        </Card>
      </div>
    </div>;
}
