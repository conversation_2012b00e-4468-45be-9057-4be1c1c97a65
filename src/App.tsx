import React from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import AuthProvider from 'react-auth-kit/AuthProvider';
import JobDetails from '@/pages/JobDetails';
import FindProPage from '@/pages/FindProPage';
import Auth from '@/pages/Auth';
import HomePage from '@/pages/HomePage';
import { authStore } from '@/lib/auth';
import { Toaster } from "@/components/ui/toaster";
import { HelmetProvider } from 'react-helmet-async';
import { RoleProvider } from '@/features/auth/context/RoleContext';
import { StripeProvider } from '@/contexts/StripeContext';
import HowItWorks from './pages/HowItWorks'
import CreateJob from './pages/CreateJob'
import ProfessionalsSearch from './pages/ProfessionalsSearch'
import PlumbingService from './pages/PlumbingService'
import PlumbingProfessionals from './pages/PlumbingProfessionals'
import ElectricalService from './pages/ElectricalService'
import ElectricalProfessionals from './pages/ElectricalProfessionals'
import HVACService from './pages/HVACService'
import HVACProfessionals from './pages/HVACProfessionals'
import RoofingService from './pages/RoofingService'
import RoofingProfessionals from './pages/RoofingProfessionals'
import SolarService from './pages/SolarService'
import SolarProfessionals from './pages/SolarProfessionals'
import LandscapingProfessionals from './pages/LandscapingProfessionals'
import CleaningService from './pages/CleaningService'
import CleaningProfessionals from './pages/CleaningProfessionals'
import HandymanService from './pages/HandymanService'
import HandymanProfessionals from './pages/HandymanProfessionals'
import PestControlService from './pages/PestControlService'
import PestControlProfessionals from './pages/PestControlProfessionals'
import ApplianceRepairService from './pages/ApplianceRepairService'
import ApplianceRepairProfessionals from './pages/ApplianceRepairProfessionals'
import ProLanding from './pages/ProLanding'
import JobListings from './pages/JobListings'
import Jobs from './pages/Jobs'
import Messages from './pages/Messages'
import FAQ from './pages/FAQ'
import AboutUs from './pages/AboutUs'
import Blog from './pages/Blog'
import BlogArticle from './pages/BlogArticle'
import BlogArticleExample from './pages/BlogArticleExample'
import Careers from './pages/Careers'
import Terms from './pages/Terms'
import Privacy from './pages/Privacy'
import CustomerDashboard from './pages/CustomerDashboard'
import FreeTools from './pages/FreeTools'
import RoofingCalculator from './pages/RoofingCalculator'
import HouseCleaningCalculator from './pages/HouseCleaningCalculator'
import LawnCareCalculator from './pages/LawnCareCalculator'
import PricingCalculator from './pages/PricingCalculator'
import LaborCostCalculator from './pages/LaborCostCalculator'
import ProfitMarginCalculator from './pages/ProfitMarginCalculator'
import PricingGuide from './pages/PricingGuide'
import MarketingGuide from './pages/MarketingGuide'
import OperationsGuide from './pages/OperationsGuide'
import ProfitForecastTool from './pages/ProfitForecastTool'
import SchedulingAssistant from './pages/SchedulingAssistant'
import InvoiceTemplates from './pages/InvoiceTemplates'
import WorkOrderTemplate from './pages/WorkOrderTemplate'
import ReceiptGenerator from './pages/ReceiptGenerator'
import ImageEditor from './pages/ImageEditor'
import Cookies from './pages/Cookies'
import Financing from './pages/Financing'
import FinancingWaitlist from './pages/FinancingWaitlist'
import FinancingRateCheck from './pages/FinancingRateCheck'
import ProviderSignup from './pages/ProviderSignup'
import ServiceProfessionals from "./components/ProfessionalsPage/ServiceProfessionals.tsx";
import JobBids from './pages/JobBids';

// Import route components
import ProviderRoutes from '@/routes/ProviderRoutes';
import CustomerRoutes from "@/routes/CustomerRoutes";
import AdminRoutes from '@/routes/AdminRoutes';
import LandscapingService from './pages/LandscapingService';
import ForProviders from './pages/ForProviders';
import RequireAuth from "@auth-kit/react-router/RequireAuth";
import StripeSuccess from './pages/StripeSuccess';
import StripeCancel from './pages/StripeCancel';

function App() {
  return (
    <HelmetProvider>
      <AuthProvider store={authStore}>
        <RoleProvider>
          <StripeProvider>
            <Router>
            <Routes>

              {/* Public Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/for-providers" element={<ForProviders />} />
              <Route path="/how-it-works" element={<HowItWorks />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/create-job" element={<CreateJob />} />
              <Route path="/professionals" element={<ProfessionalsSearch />} />
              <Route path="/find-pro-mobile" element={<FindProPage />} />

              {/* Service and Professionals Routes */}
              <Route path="/services/plumbing" element={<PlumbingService />} />
              <Route path="/professionals/plumbing" element={<PlumbingProfessionals />} />
              <Route path="/services/electrical" element={<ElectricalService />} />
              <Route path="/professionals/electrical" element={<ElectricalProfessionals />} />
              <Route path="/services/hvac" element={<HVACService />} />
              <Route path="/professionals/hvac" element={<HVACProfessionals />} />
              <Route path="/services/roofing" element={<RoofingService />} />
              <Route path="/professionals/roofing" element={<RoofingProfessionals />} />
              <Route path="/services/solar" element={<SolarService />} />
              <Route path="/professionals/solar" element={<SolarProfessionals />} />
              <Route path="/services/landscaping" element={<LandscapingService />} />
              <Route path="/professionals/landscaping" element={<LandscapingProfessionals />} />
              <Route path="/services/cleaning" element={<CleaningService />} />
              <Route path="/professionals/cleaning" element={<CleaningProfessionals />} />
              <Route path="/services/handyman" element={<HandymanService />} />
              <Route path="/professionals/handyman" element={<HandymanProfessionals />} />
              <Route path="/services/pest-control" element={<PestControlService />} />
              <Route path="/professionals/pest-control" element={<PestControlProfessionals />} />
              <Route path="/services/appliance-repair" element={<ApplianceRepairService />} />
              <Route path="/professionals/appliance-repair" element={<ApplianceRepairProfessionals />} />

              {/* Route for dynamic service professionals */}
              <Route path="/professionals/:serviceId" element={<ServiceProfessionals />} />

              {/* Job Related Routes */}
              <Route path="/job/:jobId" element={<JobDetails />} />
              <Route path="/pro-landing" element={<ProLanding />} />
              <Route path="/job-listings" element={<JobListings />} />
              <Route path="/jobs" element={<Jobs />} />
              <Route path="/messages" element={<Messages />} />

              {/* General Information Routes */}
              <Route path="/faq" element={<FAQ />} />
              <Route path="/about" element={<AboutUs />} />
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:slug" element={<BlogArticle />} />
              <Route path="/blog/example-article" element={<BlogArticleExample />} />
              <Route path="/careers" element={<Careers />} />
              <Route path="/terms" element={<Terms />} />
              <Route path="/privacy" element={<Privacy />} />
              <Route path="/cookies" element={<Cookies />} />
              <Route path="/financing" element={<Financing />} />
              <Route path="/financing/rate-check" element={<FinancingRateCheck />} />
              <Route path="/financing-waitlist" element={<FinancingWaitlist />} />
              <Route path="/provider-signup" element={<ProviderSignup />} />

              {/* Stripe Routes */}
              <Route path="/stripe/success" element={<StripeSuccess />} />
              <Route path="/stripe/cancel" element={<StripeCancel />} />

              {/* Protected Routes by User Type */}
              {/* Provider Routes */}
              <Route path="/provider/*" element={<ProviderRoutes />} />

              {/* Customer Routes */}
              <Route path="/customer/*" element={<CustomerRoutes />} />

              {/* Admin Routes */}
              <Route path="/admin/*" element={<AdminRoutes />} />

              {/* If no route matches */}
              <Route path="*" element={<Navigate to="/" />} />

              {/* Free Tools Pages */}
              <Route path="/free-tools" element={<FreeTools />} />
              <Route path="/free-tools/pricing-calculator" element={<PricingCalculator />} />
              <Route path="/free-tools/profit-calculator" element={<ProfitMarginCalculator />} />
              <Route path="/free-tools/invoice-templates" element={<InvoiceTemplates />} />
              <Route path="/free-tools/labor-cost-calculator" element={<LaborCostCalculator />} />
              <Route path="/free-tools/house-cleaning-calculator" element={<HouseCleaningCalculator />} />
              <Route path="/free-tools/lawn-care-calculator" element={<LawnCareCalculator />} />
              <Route path="/free-tools/roofing-calculator" element={<RoofingCalculator />} />
              <Route path="/free-tools/receipt-generator" element={<ReceiptGenerator />} />
              <Route path="/free-tools/work-order-template" element={<WorkOrderTemplate />} />
              <Route path="/free-tools/profit-forecast" element={<ProfitForecastTool />} />
              <Route path="/free-tools/image-editor" element={<ImageEditor />} />
              <Route path="/free-tools/pricing-guide" element={<PricingGuide />} />
              <Route path="/free-tools/marketing-guide" element={<MarketingGuide />} />
              <Route path="/free-tools/operations-guide" element={<OperationsGuide />} />
              <Route path="/free-tools/scheduling-assistant" element={<SchedulingAssistant />} />
            </Routes>
            <Toaster />
          </Router>
          </StripeProvider>
        </RoleProvider>
      </AuthProvider>
    </HelmetProvider>
  );
}

export default App;
